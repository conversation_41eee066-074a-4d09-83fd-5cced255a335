import classNames from "classnames";
import * as React from "react";

import { graphql, useQuery } from "../graphql/client.js";
import { useAuthentication } from "./Authentication.js";
import { useRouter } from "./Router.js";
import { useSearch } from "./Search.js";

const GetRandomTopMovie = graphql(/* GraphQL */ `
  query GetRandomTopMovie($offset: Int!) {
    movies(sort: BEST_FIRST, offset: $offset, limit: 1) {
      id
      title
      topPositionAllTime
      images {
        sizes {
          height
          url
          width
        }
      }
    }
  }
`);

interface NavLinkProps {
  href: string;
  disabled: boolean;
  selected: boolean;
}

const NavLink: React.FC<NavLinkProps> = (props) => {
  return props.disabled ? (
    <span
      className={classNames(
        "mt-[-7px] inline-block cursor-default rounded-full px-5 pt-[7px] pb-[10px] whitespace-pre",
        {
          "bg-zinc-900 text-white": props.selected,
        },
      )}
    >
      {props.children}
    </span>
  ) : (
    <a
      className={classNames(
        "mt-[-7px] inline-block rounded-full px-5 pt-[7px] pb-[10px] whitespace-pre transition-colors hover:transition-none",
        {
          "hover:text-zinc-500": !props.selected,
          "bg-zinc-900 text-white hover:bg-zinc-700": props.selected,
        },
      )}
      href={props.href}
    >
      {props.children}
    </a>
  );
};

const Header: React.FC = () => {
  const authentication = useAuthentication();
  const router = useRouter();
  const search = useSearch();
  const goToSearch = React.useCallback(() => {
    search.setOpen(true);
  }, [search]);
  const pattern = router.pattern;
  const [offset] = React.useState(Math.floor(Math.random() * 1000) + 1);

  // Use a stable random offset that matches server-side prefetching
  // The server prefetches with a random offset, so we need to find that data
  const { data: randomMovieData } = useQuery(
    GetRandomTopMovie,
    { offset }, // This will be overridden by server-prefetched data
    {
      enabled: pattern !== "/top",
      networkMode: typeof window === "undefined" ? "always" : "offlineFirst",
      staleTime: Infinity,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
    },
  );

  const randomMovie = randomMovieData?.movies[0];

  return (
    <header className="relative container mx-auto h-[126px] px-5 text-zinc-900 antialiased">
      {authentication.state === "authenticated" ? (
        <p className="pt-1.5 text-sm">
          Вы вошли как {authentication.user.name}
        </p>
      ) : (
        <p className="pt-1.5 text-sm">
          Вы смотрите глазами демо-пользователя.{" "}
          <button className="underline" onClick={authentication.authenticate}>
            Войти?
          </button>
        </p>
      )}

      <div className="relative mt-6 flex justify-between text-2xl font-semibold">
        <button
          className="absolute left-0 transition-colors hover:text-zinc-500 hover:transition-none"
          type="button"
          onClick={goToSearch}
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="mr-2 inline-block translate-x-[2px] translate-y-[-2px]"
          >
            <g clipPath="url(#clip0_109_815)">
              <path
                d="M15.213 13.799L11.208 9.794C11.8862 8.82953 12.2498 7.67905 12.249 6.5C12.249 4.97501 11.6432 3.51247 10.5649 2.43414C9.48655 1.3558 8.02402 0.75 6.49902 0.75C4.97403 0.75 3.51149 1.3558 2.43316 2.43414C1.35483 3.51247 0.749023 4.97501 0.749023 6.5C0.749023 8.02499 1.35483 9.48753 2.43316 10.5659C3.51149 11.6442 4.97403 12.25 6.49902 12.25C7.67807 12.2508 8.82856 11.8872 9.79302 11.209L13.798 15.214L15.213 13.799ZM2.25002 6.501C2.25002 5.94275 2.35998 5.38997 2.57361 4.87421C2.78724 4.35846 3.10037 3.88983 3.49511 3.49509C3.88985 3.10035 4.35848 2.78722 4.87424 2.57359C5.38999 2.35996 5.94277 2.25 6.50102 2.25C7.05927 2.25 7.61206 2.35996 8.12781 2.57359C8.64357 2.78722 9.11219 3.10035 9.50694 3.49509C9.90168 3.88983 10.2148 4.35846 10.4284 4.87421C10.6421 5.38997 10.752 5.94275 10.752 6.501C10.752 7.62844 10.3042 8.70969 9.50694 9.50691C8.70972 10.3041 7.62846 10.752 6.50102 10.752C5.37359 10.752 4.29233 10.3041 3.49511 9.50691C2.6979 8.70969 2.25002 7.62844 2.25002 6.501Z"
                fill="currentColor"
              />
            </g>
            <defs>
              <clipPath id="clip0_109_815">
                <rect width="16" height="16" fill="white" />
              </clipPath>
            </defs>
          </svg>
          Поиск
        </button>

        <nav className="mx-auto flex space-x-18">
          <NavLink
            href={router.stringify("/:user", {
              user: authentication.user.slug,
            })}
            disabled={
              pattern === "/:user" &&
              router.params.user === authentication.user.slug
            }
            selected={
              (pattern === "/:user" ||
                pattern === "/:user/top" ||
                pattern === "/:user/decades" ||
                pattern === "/:user/directors" ||
                pattern === "/:user/genres") &&
              router.params.user === authentication.user.slug
            }
          >
            ← Позырил
          </NavLink>

          <svg
            width="113"
            height="33"
            viewBox="0 0 113 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="inline-block translate-y-[0px]"
          >
            <path
              d="M9.44141 15.6113V10.6543H15.6992C16.9766 10.6543 17.9609 10.4492 18.6523 10.0391C19.3438 9.62891 19.6895 9.03125 19.6895 8.24609V8.21094C19.6895 7.67187 19.4844 7.22656 19.0742 6.875C18.6641 6.51172 18.0547 6.23633 17.2461 6.04883C16.4375 5.84961 15.4355 5.75 14.2402 5.75C12.8926 5.73828 11.7676 5.84961 10.8652 6.08398C9.96289 6.30664 9.25977 6.64648 8.75586 7.10352C8.26367 7.54883 7.95898 8.10547 7.8418 8.77344L7.80664 8.91406H0.652344L0.669922 8.7207C0.798828 6.9043 1.4082 5.36328 2.49805 4.09766C3.59961 2.82031 5.14062 1.8418 7.12109 1.16211C9.11328 0.482422 11.5039 0.142578 14.293 0.142578C16.9766 0.142578 19.2676 0.423828 21.166 0.986328C23.0762 1.54883 24.5352 2.36328 25.543 3.42969C26.5625 4.49609 27.0723 5.7793 27.0723 7.2793V7.31445C27.0723 8.65039 26.6094 9.79883 25.6836 10.7598C24.7695 11.709 23.5156 12.3418 21.9219 12.6582V12.7988C23.7969 13.0098 25.2793 13.6367 26.3691 14.6797C27.459 15.7227 28.0039 17.0352 28.0039 18.6172V18.6523C28.0039 20.2695 27.4648 21.6699 26.3867 22.8535C25.3203 24.0254 23.7852 24.9336 21.7812 25.5781C19.7773 26.2109 17.3867 26.5273 14.6094 26.5273C11.6562 26.5273 9.125 26.1875 7.01562 25.5078C4.90625 24.8281 3.27148 23.8555 2.11133 22.5898C0.962891 21.3125 0.347656 19.7832 0.265625 18.002V17.7383H7.63086L7.66602 17.8613C7.77148 18.5293 8.08789 19.0918 8.61523 19.5488C9.1543 19.9941 9.89844 20.334 10.8477 20.5684C11.8086 20.8027 12.998 20.9199 14.416 20.9199C15.6582 20.9199 16.707 20.8086 17.5625 20.5859C18.4297 20.3633 19.0859 20.0469 19.5312 19.6367C19.9766 19.2148 20.1992 18.7051 20.1992 18.1074V18.0723C20.1992 17.252 19.8242 16.6367 19.0742 16.2266C18.3242 15.8164 17.1934 15.6113 15.6816 15.6113H9.44141ZM29.9122 26V6.94531H37.3302V11.709H43.6231C46.2247 11.709 48.2813 12.3477 49.793 13.625C51.3048 14.8906 52.0606 16.6191 52.0606 18.8105V18.8457C52.0606 21.0605 51.3048 22.8066 49.793 24.084C48.293 25.3613 46.2364 26 43.6231 26H29.9122ZM41.7774 16.4727H37.3302V21.0605H41.7774C42.7149 21.0605 43.4298 20.8672 43.922 20.4805C44.4259 20.082 44.6778 19.5195 44.6778 18.793V18.7578C44.6778 18.0078 44.4317 17.4395 43.9395 17.0527C43.4473 16.666 42.7266 16.4727 41.7774 16.4727ZM53.9239 26V6.94531H61.3595V26H53.9239ZM63.6896 32.1523V6.94531H71.1076V10.4258H71.2306C71.6877 9.65234 72.2912 8.9668 73.0412 8.36914C73.7912 7.77148 74.6759 7.30859 75.6955 6.98047C76.7267 6.64063 77.8869 6.4707 79.1759 6.4707C81.1681 6.4707 82.9084 6.88086 84.3966 7.70117C85.8966 8.50977 87.0627 9.66406 87.8947 11.1641C88.7267 12.6523 89.1427 14.416 89.1427 16.4551V16.4902C89.1427 18.5293 88.7267 20.2988 87.8947 21.7988C87.0627 23.2871 85.9025 24.4414 84.4142 25.2617C82.9259 26.0703 81.1916 26.4746 79.2111 26.4746C77.922 26.4746 76.756 26.3105 75.713 25.9824C74.6818 25.6543 73.7912 25.1973 73.0412 24.6113C72.2912 24.0254 71.6877 23.3457 71.2306 22.5723H71.1076V32.1523H63.6896ZM76.381 21.2891C77.4357 21.2891 78.3556 21.0957 79.1408 20.709C79.9259 20.3223 80.5295 19.7715 80.9513 19.0566C81.3732 18.3418 81.5841 17.4805 81.5841 16.4727V16.4551C81.5841 15.4473 81.3673 14.5859 80.9338 13.8711C80.5119 13.1562 79.9084 12.6113 79.1232 12.2363C78.3498 11.8496 77.4357 11.6562 76.381 11.6562C75.3146 11.6562 74.383 11.8496 73.5861 12.2363C72.8009 12.623 72.1916 13.1738 71.758 13.8887C71.3244 14.6035 71.1076 15.459 71.1076 16.4551V16.4902C71.1076 17.4746 71.3244 18.3301 71.758 19.0566C72.1916 19.7715 72.8009 20.3223 73.5861 20.709C74.383 21.0957 75.3146 21.2891 76.381 21.2891ZM90.8577 26V6.94531H98.2756V11.709H104.569C107.17 11.709 109.227 12.3477 110.739 13.625C112.25 14.8906 113.006 16.6191 113.006 18.8105V18.8457C113.006 21.0605 112.25 22.8066 110.739 24.084C109.239 25.3613 107.182 26 104.569 26H90.8577ZM102.723 16.4727H98.2756V21.0605H102.723C103.66 21.0605 104.375 20.8672 104.867 20.4805C105.371 20.082 105.623 19.5195 105.623 18.793V18.7578C105.623 18.0078 105.377 17.4395 104.885 17.0527C104.393 16.666 103.672 16.4727 102.723 16.4727Z"
              fill="black"
            />
          </svg>

          <NavLink
            href={router.stringify("/", {})}
            disabled={pattern === "/"}
            selected={pattern === "/" || pattern === "/movies/genre/:genre"}
          >
            На позырить →
          </NavLink>
        </nav>

        <div className="absolute top-[-12px] right-0">
          <figure className="relative">
            {randomMovie?.images?.[0]?.sizes?.[0] ? (
              <img
                className="aspect-[1280/720] w-[110px]"
                alt=""
                src={randomMovie.images[0].sizes[0].url}
                width={randomMovie.images[0].sizes[0].width}
                height={randomMovie.images[0].sizes[0].height}
              />
            ) : (
              <div className="aspect-[1280/720] w-[110px] bg-zinc-100" />
            )}
            {pattern === "/top" ? (
              <span className="absolute inset-0 flex w-full items-center justify-center bg-black text-white">
                Топ
              </span>
            ) : (
              <a
                href={router.stringify("/top", {})}
                className="absolute inset-0 flex w-full items-center justify-center bg-black/20 text-white transition-colors hover:bg-black/30 hover:transition-none"
              >
                Топ
              </a>
            )}
          </figure>
          <figcaption className="mt-[2px] max-w-[110px] text-center text-xs leading-tight">
            {randomMovie?.title && randomMovie?.topPositionAllTime
              ? `${randomMovie.title} №${randomMovie.topPositionAllTime}`
              : null}
          </figcaption>
        </div>
      </div>

      {/* {pattern === "/" || pattern === "/:user/feed" ? (
        <svg
          width="130"
          height="41"
          viewBox="0 0 130 41"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="box-content inline-block shrink-0 pt-3 pr-0.5 pl-5 text-indigo-600"
        >
          <path
            d="M0.164062 7.67969C1.69531 5.21875 3.68229 3.36849 6.125 2.12891C8.54948 0.907552 11.2109 0.296875 14.1094 0.296875C16.388 0.296875 18.457 0.74349 20.3164 1.63672C22.2122 2.54818 23.7344 3.86068 24.8828 5.57422C26.013 7.34245 26.5781 9.375 26.5781 11.6719C26.5781 13.276 26.2865 14.6797 25.7031 15.8828C25.138 17.0312 24.1992 18.1797 22.8867 19.3281C23.9987 19.9479 24.9648 20.7227 25.7852 21.6523C26.5872 22.6003 27.2161 23.6667 27.6719 24.8516C28.1094 26.0182 28.3281 27.2214 28.3281 28.4609C28.3281 30.6302 27.918 32.4987 27.0977 34.0664C26.2227 35.6706 25.0833 36.9648 23.6797 37.9492C22.1849 38.9883 20.5716 39.7357 18.8398 40.1914C17.0352 40.6654 15.1758 40.9023 13.2617 40.9023C11.6029 40.9023 9.98047 40.6471 8.39453 40.1367C6.77214 39.6263 5.23177 38.888 3.77344 37.9219C2.27865 36.9375 1.02083 35.8529 0 34.668L6.31641 28.2422C7.3737 29.6094 8.41276 30.6029 9.43359 31.2227C10.4727 31.8789 11.7305 32.207 13.207 32.207C14.7565 32.207 16.0326 31.8151 17.0352 31.0312C18.0378 30.2474 18.5391 29.0534 18.5391 27.4492C18.5391 25.8451 17.9922 24.7695 16.8984 24.2227C15.7865 23.694 14.2917 23.4297 12.4141 23.4297H10.8828C10.7188 23.4479 10.4727 23.457 10.1445 23.457H9.73438L9.29688 23.4844V16.7031C9.66146 16.7031 10.1081 16.694 10.6367 16.6758H11.3477L12.0859 16.6484C13.0703 16.612 13.9727 16.5026 14.793 16.3203C15.5951 16.138 16.2695 15.7461 16.8164 15.1445C17.3268 14.5794 17.582 13.7409 17.582 12.6289C17.582 11.1341 17.1445 10.0221 16.2695 9.29297C15.3763 8.5638 14.2279 8.19922 12.8242 8.19922C12.168 8.19922 11.5755 8.29036 11.0469 8.47266C10.5911 8.63672 10.1172 8.86458 9.625 9.15625C9.26042 9.41146 8.76823 9.8125 8.14844 10.3594C8.02083 10.4688 7.88411 10.5872 7.73828 10.7148C7.64714 10.806 7.52865 10.9154 7.38281 11.043C7.09115 11.2982 6.89974 11.4714 6.80859 11.5625L0.164062 7.67969ZM29.6953 39.9727H29.7227V40H45.9102C53.8581 40 58.0417 35.9714 58.4609 27.9141V27.4219C58.0417 19.3646 53.8581 15.3359 45.9102 15.3359H39.2109V1.25391H39.0742V1.19922L29.6953 1.14453V39.9727ZM39.2109 24.1133H45.2266C48.0156 24.1133 49.4102 25.3073 49.4102 27.6953C49.4102 30.0651 48.0156 31.25 45.2266 31.25H39.2109V24.1133ZM60.8398 1.19922H70.3555V40L60.8398 39.9727V1.19922ZM71.75 40V1.19922H83.3438C85.9323 1.19922 88.138 1.39974 89.9609 1.80078C91.8203 2.20182 93.4245 2.8763 94.7734 3.82422C96.1224 4.77214 97.1615 6.05729 97.8906 7.67969C98.6198 9.30208 98.9844 11.2799 98.9844 13.6133C98.9844 15.5273 98.6198 17.2409 97.8906 18.7539C97.1615 20.2669 96.1497 21.5612 94.8555 22.6367C93.543 23.7305 92.0026 24.5599 90.2344 25.125C88.4297 25.7083 86.5612 26 84.6289 26H81.293V40H71.75ZM81.293 9.42969V17.7695H84.0273C85.7773 17.7695 87.1263 17.4049 88.0742 16.6758C89.0039 15.9648 89.4688 14.9258 89.4688 13.5586C89.4688 12.2279 89.0039 11.207 88.0742 10.4961C87.1263 9.78516 85.7773 9.42969 84.0273 9.42969H81.293ZM100.352 39.9727H100.379V40H116.566C124.514 40 128.698 35.9714 129.117 27.9141V27.4219C128.698 19.3646 124.514 15.3359 116.566 15.3359H109.867V1.25391H109.73V1.19922L100.352 1.14453V39.9727ZM109.867 24.1133H115.883C118.672 24.1133 120.066 25.3073 120.066 27.6953C120.066 30.0651 118.672 31.25 115.883 31.25H109.867V24.1133Z"
            fill="currentColor"
          />
        </svg>
      ) : (
        <a
          className="h-fit shrink-0 pt-3 pr-0.5 pl-5"
          href={router.stringify("/", {})}
        >
          <svg
            width="130"
            height="41"
            viewBox="0 0 130 41"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="inline-block text-indigo-600"
          >
            <path
              d="M0.164062 7.67969C1.69531 5.21875 3.68229 3.36849 6.125 2.12891C8.54948 0.907552 11.2109 0.296875 14.1094 0.296875C16.388 0.296875 18.457 0.74349 20.3164 1.63672C22.2122 2.54818 23.7344 3.86068 24.8828 5.57422C26.013 7.34245 26.5781 9.375 26.5781 11.6719C26.5781 13.276 26.2865 14.6797 25.7031 15.8828C25.138 17.0312 24.1992 18.1797 22.8867 19.3281C23.9987 19.9479 24.9648 20.7227 25.7852 21.6523C26.5872 22.6003 27.2161 23.6667 27.6719 24.8516C28.1094 26.0182 28.3281 27.2214 28.3281 28.4609C28.3281 30.6302 27.918 32.4987 27.0977 34.0664C26.2227 35.6706 25.0833 36.9648 23.6797 37.9492C22.1849 38.9883 20.5716 39.7357 18.8398 40.1914C17.0352 40.6654 15.1758 40.9023 13.2617 40.9023C11.6029 40.9023 9.98047 40.6471 8.39453 40.1367C6.77214 39.6263 5.23177 38.888 3.77344 37.9219C2.27865 36.9375 1.02083 35.8529 0 34.668L6.31641 28.2422C7.3737 29.6094 8.41276 30.6029 9.43359 31.2227C10.4727 31.8789 11.7305 32.207 13.207 32.207C14.7565 32.207 16.0326 31.8151 17.0352 31.0312C18.0378 30.2474 18.5391 29.0534 18.5391 27.4492C18.5391 25.8451 17.9922 24.7695 16.8984 24.2227C15.7865 23.694 14.2917 23.4297 12.4141 23.4297H10.8828C10.7188 23.4479 10.4727 23.457 10.1445 23.457H9.73438L9.29688 23.4844V16.7031C9.66146 16.7031 10.1081 16.694 10.6367 16.6758H11.3477L12.0859 16.6484C13.0703 16.612 13.9727 16.5026 14.793 16.3203C15.5951 16.138 16.2695 15.7461 16.8164 15.1445C17.3268 14.5794 17.582 13.7409 17.582 12.6289C17.582 11.1341 17.1445 10.0221 16.2695 9.29297C15.3763 8.5638 14.2279 8.19922 12.8242 8.19922C12.168 8.19922 11.5755 8.29036 11.0469 8.47266C10.5911 8.63672 10.1172 8.86458 9.625 9.15625C9.26042 9.41146 8.76823 9.8125 8.14844 10.3594C8.02083 10.4688 7.88411 10.5872 7.73828 10.7148C7.64714 10.806 7.52865 10.9154 7.38281 11.043C7.09115 11.2982 6.89974 11.4714 6.80859 11.5625L0.164062 7.67969ZM29.6953 39.9727H29.7227V40H45.9102C53.8581 40 58.0417 35.9714 58.4609 27.9141V27.4219C58.0417 19.3646 53.8581 15.3359 45.9102 15.3359H39.2109V1.25391H39.0742V1.19922L29.6953 1.14453V39.9727ZM39.2109 24.1133H45.2266C48.0156 24.1133 49.4102 25.3073 49.4102 27.6953C49.4102 30.0651 48.0156 31.25 45.2266 31.25H39.2109V24.1133ZM60.8398 1.19922H70.3555V40L60.8398 39.9727V1.19922ZM71.75 40V1.19922H83.3438C85.9323 1.19922 88.138 1.39974 89.9609 1.80078C91.8203 2.20182 93.4245 2.8763 94.7734 3.82422C96.1224 4.77214 97.1615 6.05729 97.8906 7.67969C98.6198 9.30208 98.9844 11.2799 98.9844 13.6133C98.9844 15.5273 98.6198 17.2409 97.8906 18.7539C97.1615 20.2669 96.1497 21.5612 94.8555 22.6367C93.543 23.7305 92.0026 24.5599 90.2344 25.125C88.4297 25.7083 86.5612 26 84.6289 26H81.293V40H71.75ZM81.293 9.42969V17.7695H84.0273C85.7773 17.7695 87.1263 17.4049 88.0742 16.6758C89.0039 15.9648 89.4688 14.9258 89.4688 13.5586C89.4688 12.2279 89.0039 11.207 88.0742 10.4961C87.1263 9.78516 85.7773 9.42969 84.0273 9.42969H81.293ZM100.352 39.9727H100.379V40H116.566C124.514 40 128.698 35.9714 129.117 27.9141V27.4219C128.698 19.3646 124.514 15.3359 116.566 15.3359H109.867V1.25391H109.73V1.19922L100.352 1.14453V39.9727ZM109.867 24.1133H115.883C118.672 24.1133 120.066 25.3073 120.066 27.6953C120.066 30.0651 118.672 31.25 115.883 31.25H109.867V24.1133Z"
              fill="currentColor"
            />
          </svg>
        </a>
      )}*/}
    </header>
  );
};

export default Header;
